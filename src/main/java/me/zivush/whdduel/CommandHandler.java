package me.zivush.whdduel;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CommandHandler implements CommandExecutor, TabCompleter {
    private final WHDDuel plugin;
    private final ConfigManager configManager;
    private final DuelManager duelManager;
    
    public CommandHandler(WHDDuel plugin, ConfigManager configManager, DuelManager duelManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.duelManager = duelManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("duel")) {
            return handleDuelCommand(sender, args);
        } else if (command.getName().equalsIgnoreCase("dueladmin")) {
            return handleDuelAdminCommand(sender, args);
        } else if (command.getName().equalsIgnoreCase("duelaccept")) {
            return handleDuelAcceptCommand(sender, args);
        } else if (command.getName().equalsIgnoreCase("dueldeny")) {
            return handleDuelDenyCommand(sender, args);
        }
        return false;
    }

    private boolean handleDuelCommand(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("player_only")));
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check permission
        if (!player.hasPermission("whdduel.duel")) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("no_permission")));
            return true;
        }
        
        // Check arguments
        if (args.length != 1) {
            player.sendMessage(ChatColor.RED + "Usage: /duel <player> or /duel <player># for instant duel");
            return true;
        }

        // Check if it's an instant duel request (ends with #)
        String targetName = args[0];
        boolean instantDuel = targetName.endsWith("#");
        if (instantDuel) {
            targetName = targetName.substring(0, targetName.length() - 1);
        }

        // Find target player
        Player target = Bukkit.getPlayer(targetName);
        if (target == null || !target.isOnline()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("player_not_found", "player", targetName)));
            return true;
        }
        
        // Check if trying to duel self
        if (player.equals(target)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("cannot_duel_self")));
            return true;
        }
        
        // Check if player is already in a duel
        if (duelManager.isPlayerInDuel(player)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("already_in_duel")));
            return true;
        }
        
        // Check if target is already in a duel
        if (duelManager.isPlayerInDuel(target)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("target_in_duel", "target", target.getName())));
            return true;
        }

        // Handle instant duel or open GUI
        if (instantDuel) {
            // Send instant duel request with default values (all restrictions enabled)
            duelManager.sendInstantDuelRequest(player, target);
        } else {
            // Open duel setup GUI
            duelManager.openDuelSetupGUI(player, target);
        }
        return true;
    }
    
    private boolean handleDuelAdminCommand(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("player_only")));
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check permission
        if (!player.hasPermission("whdduel.admin")) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("no_permission")));
            return true;
        }
        
        // Check arguments
        if (args.length != 2) {
            player.sendMessage(ChatColor.RED + "Usage: /dueladmin setpos <1|2>");
            return true;
        }
        
        if (!args[0].equalsIgnoreCase("setpos")) {
            player.sendMessage(ChatColor.RED + "Usage: /dueladmin setpos <1|2>");
            return true;
        }
        
        // Parse position number
        int position;
        try {
            position = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("invalid_position")));
            return true;
        }
        
        if (position != 1 && position != 2) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("invalid_position")));
            return true;
        }
        
        // Set arena position
        configManager.setArenaPosition(position, player.getLocation());
        player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
            configManager.getMessage("position_set", "position", String.valueOf(position))));
        
        if (configManager.isDebugEnabled()) {
            plugin.getLogger().info("Arena position " + position + " set by " + player.getName() + 
                " at " + player.getLocation().toString());
        }
        
        return true;
    }

    private boolean handleDuelAcceptCommand(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("player_only")));
            return true;
        }

        Player player = (Player) sender;

        // Check permission
        if (!player.hasPermission("whdduel.duel")) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("no_permission")));
            return true;
        }

        // Accept the duel request
        duelManager.acceptDuelRequest(player);
        return true;
    }

    private boolean handleDuelDenyCommand(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("player_only")));
            return true;
        }

        Player player = (Player) sender;

        // Check permission
        if (!player.hasPermission("whdduel.duel")) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("no_permission")));
            return true;
        }

        // Deny the duel request
        duelManager.denyDuelRequest(player);
        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (command.getName().equalsIgnoreCase("duel")) {
            if (args.length == 1) {
                // Complete player names
                String partial = args[0].toLowerCase();
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getName().toLowerCase().startsWith(partial) && !player.equals(sender)) {
                        completions.add(player.getName());
                    }
                }
            }
        } else if (command.getName().equalsIgnoreCase("dueladmin")) {
            if (args.length == 1) {
                // Complete subcommands
                if ("setpos".startsWith(args[0].toLowerCase())) {
                    completions.add("setpos");
                }
            } else if (args.length == 2 && args[0].equalsIgnoreCase("setpos")) {
                // Complete position numbers
                if ("1".startsWith(args[1])) {
                    completions.add("1");
                }
                if ("2".startsWith(args[1])) {
                    completions.add("2");
                }
            }
        }
        
        return completions;
    }
}
